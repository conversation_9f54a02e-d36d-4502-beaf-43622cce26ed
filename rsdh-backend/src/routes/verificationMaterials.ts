import { FastifyInstance } from 'fastify';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth';
import { VerificationMaterialService } from '../services/verificationMaterialService';

export async function verificationMaterialRoutes(fastify: FastifyInstance) {
  // Add education verification material
  fastify.post('/education', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Add education verification material',
      description: 'Upload verification material for an education record',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['educationId', 'fileId', 'materialType'],
        properties: {
          educationId: { type: 'string', description: 'Education record ID' },
          fileId: { type: 'string', description: 'Uploaded file ID' },
          materialType: { 
            type: 'string', 
            enum: ['diploma', 'transcript', 'certificate', 'degree_certificate', 'other'],
            description: 'Type of verification material' 
          },
          description: { type: 'string', description: 'Optional description' }
        }
      },
      response: {
        201: {
          description: 'Verification material added successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            educationId: { type: 'string' },
            fileId: { type: 'string' },
            materialType: { type: 'string' },
            description: { type: 'string' },
            status: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            file: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                originalName: { type: 'string' },
                mimeType: { type: 'string' },
                size: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const data = request.body as {
        educationId: string;
        fileId: string;
        materialType: string;
        description?: string;
      };

      const verification = await VerificationMaterialService.addEducationVerification(data);

      return reply.status(201).send(verification);
    } catch (error) {
      request.log.error('Error adding education verification:', error);
      return reply.status(400).send({
        error: 'Bad Request',
        message: error instanceof Error ? error.message : 'Failed to add verification material'
      });
    }
  });

  // Add career verification material
  fastify.post('/career', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Add career verification material',
      description: 'Upload verification material for a career record',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['careerId', 'fileId', 'materialType'],
        properties: {
          careerId: { type: 'string', description: 'Career record ID' },
          fileId: { type: 'string', description: 'Uploaded file ID' },
          materialType: { 
            type: 'string', 
            enum: ['work_certificate', 'business_card', 'contract', 'employment_letter', 'other'],
            description: 'Type of verification material' 
          },
          description: { type: 'string', description: 'Optional description' }
        }
      },
      response: {
        201: {
          description: 'Verification material added successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            careerId: { type: 'string' },
            fileId: { type: 'string' },
            materialType: { type: 'string' },
            description: { type: 'string' },
            status: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const data = request.body as {
        careerId: string;
        fileId: string;
        materialType: string;
        description?: string;
      };

      const verification = await VerificationMaterialService.addCareerVerification(data);

      return reply.status(201).send(verification);
    } catch (error) {
      request.log.error('Error adding career verification:', error);
      return reply.status(400).send({
        error: 'Bad Request',
        message: error instanceof Error ? error.message : 'Failed to add verification material'
      });
    }
  });

  // Get education verification materials
  fastify.get('/education/:educationId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Get education verification materials',
      description: 'Get all verification materials for an education record',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['educationId'],
        properties: {
          educationId: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { educationId } = request.params as { educationId: string };

      const verifications = await VerificationMaterialService.getEducationVerifications(educationId);

      return reply.send(verifications);
    } catch (error) {
      request.log.error('Error getting education verifications:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get verification materials'
      });
    }
  });

  // Get career verification materials
  fastify.get('/career/:careerId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Get career verification materials',
      description: 'Get all verification materials for a career record',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['careerId'],
        properties: {
          careerId: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { careerId } = request.params as { careerId: string };

      const verifications = await VerificationMaterialService.getCareerVerifications(careerId);

      return reply.send(verifications);
    } catch (error) {
      request.log.error('Error getting career verifications:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get verification materials'
      });
    }
  });

  // Get all verification materials for current tutor
  fastify.get('/tutor/my-verifications', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Get my verification materials',
      description: 'Get all verification materials for the current tutor',
      security: [{ bearerAuth: [] }]
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Get tutor profile
      const tutorProfile = await fastify.prisma.tutorProfile.findUnique({
        where: { userId: request.user!.id }
      });

      if (!tutorProfile) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Tutor profile not found'
        });
      }

      const verifications = await VerificationMaterialService.getTutorVerifications(tutorProfile.id);

      return reply.send(verifications);
    } catch (error) {
      request.log.error('Error getting tutor verifications:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get verification materials'
      });
    }
  });

  // Delete education verification material
  fastify.delete('/education/:verificationId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Delete education verification material',
      description: 'Delete an education verification material',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['verificationId'],
        properties: {
          verificationId: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { verificationId } = request.params as { verificationId: string };

      await VerificationMaterialService.deleteEducationVerification(verificationId, request.user!.id);

      return reply.status(204).send();
    } catch (error) {
      request.log.error('Error deleting education verification:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Education verification not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }
        if (error.message === 'Unauthorized to delete this verification') {
          return reply.status(403).send({
            error: 'Forbidden',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to delete verification material'
      });
    }
  });

  // Delete career verification material
  fastify.delete('/career/:verificationId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Verification Materials'],
      summary: 'Delete career verification material',
      description: 'Delete a career verification material',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['verificationId'],
        properties: {
          verificationId: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { verificationId } = request.params as { verificationId: string };

      await VerificationMaterialService.deleteCareerVerification(verificationId, request.user!.id);

      return reply.status(204).send();
    } catch (error) {
      request.log.error('Error deleting career verification:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Career verification not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }
        if (error.message === 'Unauthorized to delete this verification') {
          return reply.status(403).send({
            error: 'Forbidden',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to delete verification material'
      });
    }
  });
}

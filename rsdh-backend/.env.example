# App
NODE_ENV="development"
PORT=3001
APP_NAME=my-app
APP_URL=http://localhost:3000

DATABASE_URL="postgresql://user:pass@127.0.0.1:15432/rsdh_dev"

# CORS
CORS_ORIGIN="http://localhost:3000"
TRUSTED_ORIGINS="http://localhost:3000"

# Auth Configuration
JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-random"
AUTH_URL="http://localhost:3000"

ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD=""
TEST_EMAIL=""
TEST_PASSWORD=""

SUPPORT_EMAIL=<EMAIL>

# Email Configuration; use APP_URL, APP_NAME
EMAIL_HOST=mail.example.com
EMAIL_PORT=465
EMAIL_USER=<EMAIL>
EMAIL_PASS=qafjeojeorjeorjape
EMAIL_FROM=<EMAIL>

# OAuth Configuration
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GITHUB_CALLBACK_URL=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=

# WeChat OAuth Configuration
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# SMS Configuration
SMS_PROVIDER=mock
SMS_ACCESS_KEY_ID=your_access_key_id
SMS_ACCESS_KEY_SECRET=your_access_key_secret
SMS_SIGN_NAME=人生导航
SMS_TEMPLATE_CODE=SMS_123456789

# S3 Storage Configuration
S3_PROVIDER=aws
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your_access_key_id
S3_SECRET_ACCESS_KEY=your_secret_access_key
S3_BUCKET_NAME=your_bucket_name
S3_FORCE_PATH_STYLE=false
CDN_BASE_URL=

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm,audio/mp3,audio/wav,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

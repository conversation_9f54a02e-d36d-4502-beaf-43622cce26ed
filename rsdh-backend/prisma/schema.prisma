// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id              String             @id @default(uuid())
  email           String             @unique
  name            String?
  avatar          String?            // User avatar URL
  image           String?            // Better-auth compatibility
  emailVerified   <PERSON>olean            @default(false)
  disabled        <PERSON>olean            @default(false) // User disabled flag
  password        String?
  role            String             @default("user") // user, admin

  // Phone number fields for Better-Auth phone number plugin
  phoneNumber     String?            @unique @map("phone_number")
  phoneNumberVerified Boolean        @default(false) @map("phone_number_verified")

  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @updatedAt @map("updated_at")
  sessions        Session[]
  refreshTokens   RefreshToken[]
  resetTokens     PasswordResetToken[]
  accounts        Account[]          // Better-auth accounts

  // Tutor relations
  tutorProfile    TutorProfile?      // One-to-one relation with tutor profile
  studentAppointments Appointment[] @relation("StudentAppointments") // Appointments as student

  // File relations
  uploadedFiles   File[]             // Files uploaded by this user

  @@map("users")
  @@index([email])
  @@index([role])
  @@index([phoneNumber])
}

// Better-auth Account model for OAuth providers
model Account {
  id                String  @id @default(uuid())
  userId            String  @map("user_id")
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  accountId         String  @map("account_id") // Better-auth uses accountId
  providerId        String  @map("provider_id") // Better-auth uses providerId
  password          String? @db.Text // For credential provider
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@unique([providerId, accountId])
  @@map("accounts")
  @@index([userId])
}

// Session management
model Session {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  userAgent       String?  @map("user_agent")
  ipAddress       String?  @map("ip_address")
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("sessions")
  @@index([userId])
  @@index([token])
}

// Refresh tokens for long-lived sessions
model RefreshToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("refresh_tokens")
  @@index([userId])
  @@index([token])
}

// Password reset tokens
model PasswordResetToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  used            Boolean  @default(false)
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("password_reset_tokens")
  @@index([userId])
  @@index([token])
}

// Verification codes for email verification, registration, password reset
model VerificationCode {
  id        String   @id @default(uuid())
  email     String
  code      String
  type      String   // 'registration', 'password-reset', 'email-verification'
  expiresAt DateTime @map("expires_at")
  used      Boolean  @default(false)
  createdAt DateTime @default(now()) @map("created_at")

  @@map("verification_codes")
  @@index([email, type])
  @@index([code])
  @@index([expiresAt])
}

model TutorProfile {
  id           String    @id @default(uuid())
  userId      String    @unique @map("user_id")
  title        String?
  bio          String?
  rate         Float?    @default(0.0)
  status       String    @default("pending") // pending, approved, rejected
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  education    TutorEducation[]
  career       TutorCareer[]
  availability TutorAvailability[]
  appointments Appointment[]
  reviews      Review[]

  @@map("tutors")
}

model TutorEducation {
  id              String      @id @default(uuid())
  tutorId        String
  degree          String
  fieldOfStudy  String
  institution     String
  startYear      Int
  endYear        Int?
  description     String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)

  @@map("tutor_education")
}

model TutorCareer {
  id              String      @id @default(uuid())
  tutorId        String
  title           String
  company         String
  startYear      Int
  endYear        Int?
  current         Boolean     @default(false)
  description     String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)

  @@map("tutor_career")
}

model TutorAvailability {
  id           String      @id @default(uuid())
  tutorId     String
  dayOfWeek  Int         // 0-6 (Sunday-Saturday)
  startTime   String      // Format: "HH:MM"
  endTime     String      // Format: "HH:MM"
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  tutor       TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)

  @@map("tutor_availability")
}

model Appointment {
  id              String    @id @default(uuid())
  tutorId        String
  studentId      String
  status          String    @default("scheduled") // scheduled, completed, cancelled
  meetingType    String    // online, in_person
  meetingLink    String?
  startTime      DateTime
  endTime        DateTime
  notes           String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id])
  student        User         @relation("StudentAppointments", fields: [studentId], references: [id])
  review         Review?

  @@map("appointments")
}

model Review {
  id              String      @id @default(uuid())
  appointmentId  String      @unique
  tutorId       String
  rating          Int         // 1-5
  comment         String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  appointment    Appointment @relation(fields: [appointmentId], references: [id])
  tutor          TutorProfile @relation(fields: [tutorId], references: [id])

  @@map("reviews")
}

// File storage model for S3 uploaded files
model File {
  id              String      @id @default(uuid())
  originalName    String      @map("original_name")
  fileName        String      @map("file_name") // Generated unique filename
  mimeType        String      @map("mime_type")
  size            Int         // File size in bytes
  s3Key           String      @unique @map("s3_key") // S3 object key
  s3Bucket        String      @map("s3_bucket")
  s3Region        String      @map("s3_region")
  cdnUrl          String?     @map("cdn_url") // CDN URL if available
  uploadedById    String      @map("uploaded_by_id")
  category        String      @default("general") // avatar, document, media, etc.
  isPublic        Boolean     @default(false) // Whether file is publicly accessible
  metadata        Json?       // Additional metadata (dimensions, duration, etc.)
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")

  // Relations
  uploadedBy      User        @relation(fields: [uploadedById], references: [id], onDelete: Cascade)

  @@map("files")
  @@index([uploadedById])
  @@index([category])
  @@index([s3Key])
  @@index([createdAt])
}